import React, { useRef, useState, useEffect, useMemo } from 'react';
import { useFrame } from '@react-three/fiber';
import { OrbitControls, Text } from '@react-three/drei';
import { Group } from 'three';
import { GameState, Player, TileType } from '../../types/gameTypes';
import { Ctx } from 'boardgame.io';
import { GameTile } from './GameTile';
import { GamePlayer } from './GamePlayer';

interface BrawlPartySceneProps {
  G: GameState;
  ctx: Ctx;
  moves: any;
  playerID: string | null;
  isActive: boolean;
}

export const BrawlPartyScene: React.FC<BrawlPartySceneProps> = ({
  G,
  ctx,
  moves,
  playerID,
  isActive,
}) => {
  const groupRef = useRef<Group>(null);
  const [lastMouseMove, setLastMouseMove] = useState(Date.now());
  const [shouldRotate, setShouldRotate] = useState(false);

  // Track mouse movement to control rotation
  useEffect(() => {
    const handleMouseMove = () => {
      setLastMouseMove(Date.now());
      setShouldRotate(false);
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Check if mouse has been idle for more than 1 minute
  useFrame((state, delta) => {
    const now = Date.now();
    const timeSinceLastMove = now - lastMouseMove;
    const oneMinute = 60 * 1000; // 60 seconds in milliseconds

    if (timeSinceLastMove > oneMinute && !shouldRotate) {
      setShouldRotate(true);
    }

    // Rotate the entire board slowly only when mouse is idle
    if (groupRef.current && shouldRotate) {
      groupRef.current.rotation.y += delta * 0.02; // Much slower rotation
    }
  });

  // Helper function to check if a position collides with any tile
  const isPositionOccupiedByTile = (x: number, z: number, minDistance: number = 3) => {
    return G.tiles.some(tile => {
      const distance = Math.sqrt(
        Math.pow(tile.position.x - x, 2) + Math.pow(tile.position.z - z, 2)
      );
      return distance < minDistance;
    });
  };

  // Memoize environment objects to prevent regeneration on every render
  const environmentObjects = useMemo(() => {
    const objects = {
      rocks: [] as Array<{ key: string; position: [number, number, number]; scale: number }>,
      cacti: [] as Array<{ key: string; position: [number, number, number]; height: number; hasArm: boolean }>,
      palms: [] as Array<{ key: string; position: [number, number, number]; trunkHeight: number }>,
      dunes: [] as Array<{ key: string; position: [number, number, number]; scale: number }>,
      oasisVegetation: [] as Array<{ key: string; position: [number, number, number]; scale: number }>,
      oasisPalms: [] as Array<{ key: string; position: [number, number, number]; trunkHeight: number }>,
    };

    // Generate rocks with collision detection
    for (let i = 0; i < 15; i++) {
      let attempts = 0;
      let validPosition = false;
      let x, z;

      while (!validPosition && attempts < 50) {
        const angle = (i / 15) * Math.PI * 2 + Math.random() * 0.8;
        const radius = 5 + Math.random() * 8;
        x = Math.cos(angle) * radius;
        z = Math.sin(angle) * radius;

        if (!isPositionOccupiedByTile(x, z, 2.5)) {
          validPosition = true;
        }
        attempts++;
      }

      if (validPosition) {
        const scale = 0.4 + Math.random() * 1.2;
        objects.rocks.push({
          key: `rock-${i}`,
          position: [x!, -0.8 + scale * 0.4, z!],
          scale,
        });
      }
    }

    // Generate cacti with collision detection
    for (let i = 0; i < 10; i++) {
      let attempts = 0;
      let validPosition = false;
      let x, z;

      while (!validPosition && attempts < 50) {
        const angle = (i / 10) * Math.PI * 2 + Math.random() * 0.6;
        const radius = 5 + Math.random() * 8;
        x = Math.cos(angle) * radius;
        z = Math.sin(angle) * radius;

        if (!isPositionOccupiedByTile(x, z, 2.5)) {
          validPosition = true;
        }
        attempts++;
      }

      if (validPosition) {
        const height = 2 + Math.random() * 3;
        objects.cacti.push({
          key: `cactus-${i}`,
          position: [x!, 0, z!],
          height,
          hasArm: Math.random() > 0.5,
        });
      }
    }

    // Generate palm trees with collision detection
    for (let i = 0; i < 6; i++) {
      let attempts = 0;
      let validPosition = false;
      let x, z;

      while (!validPosition && attempts < 50) {
        const angle = (i / 6) * Math.PI * 2;
        const radius = 15 + Math.random() * 5;
        x = Math.cos(angle) * radius;
        z = Math.sin(angle) * radius;

        if (!isPositionOccupiedByTile(x, z, 3)) {
          validPosition = true;
        }
        attempts++;
      }

      if (validPosition) {
        const trunkHeight = 4 + Math.random() * 2;
        objects.palms.push({
          key: `palm-${i}`,
          position: [x!, 0, z!],
          trunkHeight,
        });
      }
    }

    // Generate sand dunes with collision detection
    for (let i = 0; i < 8; i++) {
      let attempts = 0;
      let validPosition = false;
      let x, z;

      while (!validPosition && attempts < 50) {
        const angle = (i / 8) * Math.PI * 2 + Math.random() * 0.5;
        const radius = 20 + Math.random() * 5;
        x = Math.cos(angle) * radius;
        z = Math.sin(angle) * radius;

        if (!isPositionOccupiedByTile(x, z, 4)) {
          validPosition = true;
        }
        attempts++;
      }

      if (validPosition) {
        const scale = 2 + Math.random() * 3;
        objects.dunes.push({
          key: `dune-${i}`,
          position: [x!, -1.5, z!],
          scale,
        });
      }
    }

    // Generate oasis vegetation with fixed positions
    for (let i = 0; i < 16; i++) {
      const angle = (i / 16) * Math.PI * 2;
      const radius = 9 + (i % 3) * 0.5; // Use deterministic variation instead of random
      const x = Math.cos(angle) * radius;
      const z = Math.sin(angle) * radius;
      const scale = 0.4 + (i % 4) * 0.15; // Deterministic scale variation
      objects.oasisVegetation.push({
        key: `vegetation-${i}`,
        position: [x, 0, z],
        scale,
      });
    }

    // Generate oasis palm trees with fixed positions
    for (let i = 0; i < 3; i++) {
      const angle = (i / 3) * Math.PI * 2;
      const radius = 11;
      const x = Math.cos(angle) * radius;
      const z = Math.sin(angle) * radius;
      const trunkHeight = 3 + (i * 0.3); // Deterministic height variation
      objects.oasisPalms.push({
        key: `oasis-palm-${i}`,
        position: [x, 0, z],
        trunkHeight,
      });
    }

    return objects;
  }, [G.tiles]); // Only regenerate if tiles change (which should be never after initial setup)

  return (
    <>
      {/* Desert Lighting */}
      <ambientLight intensity={0.4} color="#FFF8DC" />
      <directionalLight
        position={[30, 40, 20]}
        intensity={1.8}
        color="#FFE4B5"
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
        shadow-camera-far={150}
        shadow-camera-left={-60}
        shadow-camera-right={60}
        shadow-camera-top={60}
        shadow-camera-bottom={-60}
      />
      <pointLight position={[0, 30, 0]} intensity={0.6} color="#F4A460" />
      <hemisphereLight args={['#FFE4B5', '#DEB887', 0.3]} />

      {/* Desert atmosphere - subtle warm glow */}
      <pointLight position={[20, 15, 20]} intensity={0.3} color="#FF8C00" distance={80} />
      <pointLight position={[-20, 15, -20]} intensity={0.3} color="#FF8C00" distance={80} />

      {/* Camera Controls */}
      <OrbitControls
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        minDistance={15}
        maxDistance={50}
        minPolarAngle={0}
        maxPolarAngle={Math.PI / 2}
      />



      {/* Desert Game Board */}
      <group ref={groupRef}>
        {/* Main Desert Ground - Large sandy terrain */}
        <mesh position={[0, -2.5, 0]} receiveShadow>
          <cylinderGeometry args={[50, 50, 4, 64]} />
          <meshStandardMaterial
            color="#F4A460"
            roughness={0.9}
            metalness={0.0}
          />
        </mesh>

        {/* Inner Desert Area - Slightly elevated */}
        <mesh position={[0, -0.2, 0]} receiveShadow>
          <cylinderGeometry args={[25, 25, 1, 32]} />
          <meshStandardMaterial
            color="#D2B48C"
            roughness={0.9}
            metalness={0.0}
          />
        </mesh>

        {/* Desert Rocks - Memoized to prevent regeneration */}
        {environmentObjects.rocks.map((rock) => (
          <mesh key={rock.key} position={rock.position} receiveShadow castShadow>
            <sphereGeometry args={[rock.scale, 8, 6]} />
            <meshStandardMaterial
              color="#8B7355"
              roughness={0.95}
              metalness={0.0}
            />
          </mesh>
        ))}

        {/* Desert Cacti - Memoized to prevent regeneration */}
        {environmentObjects.cacti.map((cactus) => (
          <group key={cactus.key} position={cactus.position}>
            {/* Main cactus body */}
            <mesh position={[0, cactus.height / 2, 0]} receiveShadow castShadow>
              <cylinderGeometry args={[0.3, 0.4, cactus.height, 8]} />
              <meshStandardMaterial
                color="#228B22"
                roughness={0.8}
                metalness={0.0}
              />
            </mesh>
            {/* Cactus arms */}
            {cactus.hasArm && (
              <mesh position={[0.6, cactus.height * 0.7, 0]} rotation={[0, 0, Math.PI / 2]} receiveShadow castShadow>
                <cylinderGeometry args={[0.2, 0.25, 1.5, 6]} />
                <meshStandardMaterial
                  color="#228B22"
                  roughness={0.8}
                  metalness={0.0}
                />
              </mesh>
            )}
          </group>
        ))}

        {/* Palm Trees - Memoized to prevent regeneration */}
        {environmentObjects.palms.map((palm) => (
          <group key={palm.key} position={palm.position}>
            {/* Palm trunk */}
            <mesh position={[0, palm.trunkHeight / 2, 0]} receiveShadow castShadow>
              <cylinderGeometry args={[0.4, 0.6, palm.trunkHeight, 8]} />
              <meshStandardMaterial
                color="#8B4513"
                roughness={0.9}
                metalness={0.0}
              />
            </mesh>
            {/* Palm fronds */}
            {Array.from({ length: 6 }, (_, j) => {
              const frondAngle = (j / 6) * Math.PI * 2;
              const frondX = Math.cos(frondAngle) * 2;
              const frondZ = Math.sin(frondAngle) * 2;
              return (
                <mesh
                  key={`frond-${j}`}
                  position={[frondX, palm.trunkHeight + 0.5, frondZ]}
                  rotation={[Math.PI / 6, frondAngle, 0]}
                  receiveShadow
                  castShadow
                >
                  <boxGeometry args={[0.2, 3, 0.8]} />
                  <meshStandardMaterial
                    color="#228B22"
                    roughness={0.7}
                    metalness={0.0}
                  />
                </mesh>
              );
            })}
          </group>
        ))}

        {/* Sand Dunes - Memoized to prevent regeneration */}
        {environmentObjects.dunes.map((dune) => (
          <mesh key={dune.key} position={dune.position} receiveShadow>
            <sphereGeometry args={[dune.scale, 16, 8]} />
            <meshStandardMaterial
              color="#DEB887"
              roughness={0.9}
              metalness={0.0}
            />
          </mesh>
        ))}

        {/* Central Oasis */}
        <group position={[0, 0, 0]}>
          {/* Oasis water - Raised to be visible above terrain */}
          <mesh position={[0, 0.5, 0]} receiveShadow>
            <cylinderGeometry args={[8, 8, 0.6, 32]} />
            <meshStandardMaterial
              color="#1E90FF"
              roughness={0.05}
              metalness={0.1}
              transparent
              opacity={0.9}
            />
          </mesh>

          {/* Oasis rim - Add a visible border */}
          <mesh position={[0, 0.7, 0]} receiveShadow>
            <torusGeometry args={[8, 0.3, 8, 32]} />
            <meshStandardMaterial
              color="#8B7355"
              roughness={0.8}
              metalness={0.0}
            />
          </mesh>

          {/* Oasis vegetation ring - Memoized to prevent regeneration */}
          {environmentObjects.oasisVegetation.map((vegetation) => (
            <mesh key={vegetation.key} position={vegetation.position} receiveShadow castShadow>
              <sphereGeometry args={[vegetation.scale, 8, 6]} />
              <meshStandardMaterial
                color="#32CD32"
                roughness={0.8}
                metalness={0.0}
              />
            </mesh>
          ))}

          {/* Small oasis palm trees - Memoized to prevent regeneration */}
          {environmentObjects.oasisPalms.map((palm) => (
            <group key={palm.key} position={palm.position}>
              {/* Palm trunk */}
              <mesh position={[0, palm.trunkHeight / 2, 0]} receiveShadow castShadow>
                <cylinderGeometry args={[0.3, 0.4, palm.trunkHeight, 8]} />
                <meshStandardMaterial
                  color="#8B4513"
                  roughness={0.9}
                  metalness={0.0}
                />
              </mesh>
              {/* Palm fronds */}
              {Array.from({ length: 5 }, (_, j) => {
                const frondAngle = (j / 5) * Math.PI * 2;
                const frondX = Math.cos(frondAngle) * 1.5;
                const frondZ = Math.sin(frondAngle) * 1.5;
                return (
                  <mesh
                    key={`oasis-frond-${j}`}
                    position={[frondX, palm.trunkHeight + 0.3, frondZ]}
                    rotation={[Math.PI / 6, frondAngle, 0]}
                    receiveShadow
                    castShadow
                  >
                    <boxGeometry args={[0.15, 2, 0.6]} />
                    <meshStandardMaterial
                      color="#228B22"
                      roughness={0.7}
                      metalness={0.0}
                    />
                  </mesh>
                );
              })}
            </group>
          ))}
        </group>

        {/* Game Tiles */}
        {G.tiles.map((tile) => (
          <GameTile
            key={tile.id}
            tile={tile}
            players={Object.values(G.players).filter(
              (player: Player) => player.position === tile.id
            )}
            isActive={isActive}
            moves={moves}
            playerID={playerID}
          />
        ))}

        {/* Game Players */}
        {Object.values(G.players).map((player: Player) => {
          const tile = G.tiles.find(t => t.id === player.position);
          if (!tile) return null;

          return (
            <GamePlayer
              key={player.ID}
              player={player}
              position={tile.position}
              isCurrentPlayer={ctx.currentPlayer === player.ID}
            />
          );
        })}
      </group>



      {/* Game Over */}
      {ctx.gameover && (
        <Text
          position={[0, 12, 0]}
          fontSize={1.5}
          color="#e74c3c"
          anchorX="center"
          anchorY="middle"
        >
          🎉 {G.players[ctx.gameover.winner]?.name} Wins!
        </Text>
      )}
    </>
  );
};
