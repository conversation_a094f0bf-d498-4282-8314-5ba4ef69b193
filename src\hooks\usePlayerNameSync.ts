import { useEffect, useRef } from 'react';
import { skipToken } from '@reduxjs/toolkit/query';
import { useGetMatchQuery } from '../api';
import { GameState } from '../types/gameTypes';
import { Ctx } from 'boardgame.io';

interface UsePlayerNameSyncProps {
  G: GameState;
  ctx: Ctx;
  moves: any;
  matchID?: string;
  playerID?: string | null;
}

/**
 * Hook to synchronize player names from match metadata to game state
 * This ensures that actual player nicknames are displayed instead of generic "Player 1", "Player 2" labels
 */
export const usePlayerNameSync = ({
  G,
  ctx,
  moves,
  matchID,
  playerID,
}: UsePlayerNameSyncProps) => {
  const syncedRef = useRef<Set<string>>(new Set());
  
  // Get match metadata to access player names
  const { data: matchMetadata } = useGetMatchQuery(
    matchID ?? skipToken,
    {
      // Only poll if we haven't synced all players yet
      pollingInterval: syncedRef.current.size < ctx.numPlayers ? 1000 : 0,
    }
  );

  useEffect(() => {
    // Only proceed if we have match metadata, moves, and the updatePlayerName move
    if (!matchMetadata?.players || !moves?.updatePlayerName) {
      return;
    }

    // Update player names from match metadata
    matchMetadata.players.forEach((player) => {
      const playerIDStr = player.id.toString();
      const gamePlayer = G.players[playerIDStr];
      
      // Check if this player needs name update
      if (
        player.name && // Player has a name in metadata
        gamePlayer && // Player exists in game state
        gamePlayer.name !== player.name && // Names don't match
        !syncedRef.current.has(playerIDStr) // Haven't synced this player yet
      ) {
        try {
          // Update the player name using the game move
          moves.updatePlayerName(playerIDStr, player.name);
          
          // Mark this player as synced
          syncedRef.current.add(playerIDStr);
          
          console.log(`Updated player ${playerIDStr} name from "${gamePlayer.name}" to "${player.name}"`);
        } catch (error) {
          console.error(`Failed to update player ${playerIDStr} name:`, error);
        }
      }
    });
  }, [matchMetadata, G.players, moves, ctx.numPlayers]);

  // Reset sync tracking when game restarts or match changes
  useEffect(() => {
    syncedRef.current.clear();
  }, [matchID, ctx.turn]);

  return {
    isNameSyncComplete: syncedRef.current.size >= ctx.numPlayers,
    syncedPlayerCount: syncedRef.current.size,
  };
};
