import { useEffect, useRef } from 'react';
import { skipToken } from '@reduxjs/toolkit/query';
import { useGetMatchQuery } from '../api';
import { GameState } from '../types/gameTypes';
import { Ctx } from 'boardgame.io';

interface UsePlayerNameSyncProps {
  G: GameState;
  ctx: Ctx;
  moves: any;
  matchID?: string;
  playerID?: string | null;
}

/**
 * Hook to synchronize player names from match metadata to game state
 * This ensures that actual player nicknames are displayed instead of generic "Player 1", "Player 2" labels
 */
export const usePlayerNameSync = ({
  G,
  ctx,
  moves,
  matchID,
  playerID,
}: UsePlayerNameSyncProps) => {
  const syncedRef = useRef<Set<string>>(new Set());

  // Get match metadata to access player names
  const { data: matchMetadata } = useGetMatchQuery(
    matchID ?? skipToken,
    {
      // Only poll if we haven't synced all players yet
      pollingInterval: syncedRef.current.size < ctx.numPlayers ? 1000 : 0,
    }
  );

  useEffect(() => {
    // Only proceed if we have match metadata, moves, and the updatePlayerName move
    if (!matchMetadata?.players || !moves?.updatePlayerName) {
      return;
    }

    // Update player names from match metadata
    // Strategy: Only update the current player's name when it's their turn
    // This avoids permission issues with boardgame.io move restrictions
    const currentPlayerIDStr = ctx.currentPlayer;
    const currentPlayerMetadata = matchMetadata.players.find(p => p.id.toString() === currentPlayerIDStr);
    const currentGamePlayer = G.players[currentPlayerIDStr];

    // Only update if it's this client's player's turn and they need a name update
    if (
      playerID === currentPlayerIDStr && // Only update when it's this client's player's turn
      currentPlayerMetadata?.name && // Player has a name in metadata
      currentGamePlayer && // Player exists in game state
      currentGamePlayer.name !== currentPlayerMetadata.name && // Names don't match
      !syncedRef.current.has(currentPlayerIDStr) // Haven't synced this player yet
    ) {
      try {
        console.log(`🔄 Syncing player ${currentPlayerIDStr} name: "${currentGamePlayer.name}" → "${currentPlayerMetadata.name}"`);

        // Update the player name using the game move
        moves.updatePlayerName(currentPlayerIDStr, currentPlayerMetadata.name);

        // Mark this player as synced
        syncedRef.current.add(currentPlayerIDStr);
      } catch (error) {
        console.error(`❌ Failed to sync player ${currentPlayerIDStr} name:`, error);
      }
    }
  }, [matchMetadata, G.players, moves, ctx.numPlayers, ctx.currentPlayer, playerID]);

  // Reset sync tracking when game restarts or match changes
  useEffect(() => {
    syncedRef.current.clear();
  }, [matchID, ctx.turn]);

  return {
    isNameSyncComplete: syncedRef.current.size >= ctx.numPlayers,
    syncedPlayerCount: syncedRef.current.size,
  };
};
