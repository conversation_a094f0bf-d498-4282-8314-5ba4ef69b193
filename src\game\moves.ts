import { Move } from 'boardgame.io';
import { GameState } from '../types/gameTypes';
import { applyTileEffect } from './index';

export const rollDice: Move<GameState> = ({ G, ctx, random, events }) => {
  const playerId = ctx.currentPlayer;
  const player = G.players[playerId];

  // Roll a 6-sided die
  const diceValue = random.D6();
  G.currentDiceRoll = diceValue;

  // Add to game log
  G.gameLog.push({
    playerId,
    action: 'dice_roll',
    details: `${player.name} rolled a ${diceValue}`,
    timestamp: Date.now(),
  });

  // Automatically move the player
  const newPosition = ((player.position - 1 + diceValue) % G.gameConfig.boardSize) + 1;
  G.players[playerId].position = newPosition;

  // Add movement to log
  G.gameLog.push({
    playerId,
    action: 'move',
    details: `${player.name} moved to tile ${newPosition}`,
    timestamp: Date.now(),
  });

  // Apply tile effect
  applyTileEffect(G, playerId);

  // End turn
  events.endTurn();
};

export const movePlayer: Move<GameState> = ({ G, ctx }, steps: number) => {
  const playerId = ctx.currentPlayer;
  const player = G.players[playerId];

  // Calculate new position (wrapping around the board)
  const newPosition = ((player.position - 1 + steps) % G.gameConfig.boardSize) + 1;
  G.players[playerId].position = newPosition;

  // Add to game log
  G.gameLog.push({
    playerId,
    action: 'move',
    details: `${player.name} moved ${steps} steps to tile ${newPosition}`,
    timestamp: Date.now(),
  });

  // Apply tile effect
  applyTileEffect(G, playerId);
};

export const openTreasureChest: Move<GameState> = ({ G, ctx, events }) => {
  const playerId = ctx.currentPlayer;
  const player = G.players[playerId];

  // Check if player is on treasure chest tile
  if (player.position !== G.gameConfig.treasureChestPosition) {
    return; // Invalid move
  }

  // Check if player has enough keys
  if (player.keys >= G.gameConfig.keysToWin) {
    // Player wins!
    G.gameLog.push({
      playerId,
      action: 'treasure_chest_opened',
      details: `${player.name} opened the treasure chest and won the game!`,
      timestamp: Date.now(),
    });

    events.endGame({
      winner: playerId,
    });
  } else {
    // Not enough keys
    G.gameLog.push({
      playerId,
      action: 'treasure_chest_failed',
      details: `${player.name} tried to open the treasure chest but needs ${G.gameConfig.keysToWin - player.keys} more keys`,
      timestamp: Date.now(),
    });
  }
};

export const updatePlayerName: Move<GameState> = ({ G, ctx }, playerID: string, newName: string) => {
  // Only allow current player to update their own name
  if (ctx.currentPlayer === playerID) {
    if (G.players[playerID]) {
      const oldName = G.players[playerID].name;
      G.players[playerID].name = newName;

      console.log(`✅ Player name synced: ${playerID} "${oldName}" → "${newName}"`);

      // Add to game log
      G.gameLog.push({
        playerId: playerID,
        action: 'name_update',
        details: `Player name updated from "${oldName}" to "${newName}"`,
        timestamp: Date.now(),
      });
    }
  }
};
